import * as z from "zod";

export const orderValidationSchema = z.object({
  productId: z.string().min(1, { message: "Product selection is required" }),
  customerId: z.string().min(1, { message: "Customer selection is required" }),
  deliveryMethod: z.enum(["CUSTOM", "NO_CHARGE"]),
  customeDeliveryOptions: z
    .object({
      name: z.string().min(1, "Delivery name is required"),
      rate: z.number().min(0, "Rate must be non-negative"),
      schedule: z.boolean(),
      date: z.string().optional(),
      fromTime: z.string().optional(),
      totime: z.string().optional(),
      pickup: z.boolean(),
    })
    .optional()
    .refine(
      (val) => {
        if (!val?.schedule) return true;
        return !!val?.date && !!val?.fromTime && !!val?.totime;
      },
      {
        message: "Date and time range are required when scheduling",
        path: ["date"],
      }
    ),
  feeOptions: z
    .array(
      z.object({
        name: z.string().min(1, "Fee name is required"),
        rate: z.number().min(0, "Rate must be non-negative"),
      })
    )
    .default([]),
  discountOptions: z
    .array(
      z.object({
        name: z.string().min(1, "Discount name is required"),
        rate: z.number().min(0, "Rate must be non-negative"),
      })
    )
    .default([]),
  location: z.object({
    useLocation: z.boolean(),
    address: z.string().refine(
      (val) => {
        // If useLocation is true, address should not be empty
        return !val || val.trim().length > 0;
      },
      {
        message: "Address is required when location is enabled",
      }
    ),
  }),
});

import { Button, useDisclosure } from "@heroui/react";
import { <PERSON>i<PERSON><PERSON>cil, BiTrash } from "react-icons/bi";
import ModalComponent from "../ModalComponent";
import { FiAlertTriangle } from "react-icons/fi";
import { OrderSchema } from "../../types/orderType";
import { UseFormSetValue } from "react-hook-form";
import CustomerFormModal from "../customers/CustomerFormModal";
import { CustomerType } from "../../types/customerType";
import { useCustomerFormModal } from "../../hooks/useCustomerFormModal";

interface SelectedCustomerProps {
	setValue: UseFormSetValue<OrderSchema>;
	customerId: string;
	users: CustomerType[];
}
const SelectedCustomer = ({ setValue, users = [], customerId = "" }: SelectedCustomerProps) => {
	const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
	// Use the customer form modal hook
	const { customerFormModal, openEditModal } = useCustomerFormModal();
	const handleDeleteCustomer = () => {
		setValue("customerId", "");
		onClose();
	};

	if (customerId === "") return null;
	const customer = users.find((user) => user._id === customerId);
	// steps
	// need to fetch the customer details from the grapqhl query
	// and also add the edit and delete button here
	// and also add the customer details here

	// when clicked on the edit button, it should open the customer edit modal
	// when clicked on the delete button, it should open the customer delete modal and i need to delete the selectedCustomerId from here stored in the orders slice.
	return (
		<div className="flex w-full justify-between items-center">
			<ModalComponent
				saveButtonText={"Delete"}
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				onPress={handleDeleteCustomer}
			>
				<div className="w-full flex flex-col gap-4 items-center justify-center p-4">
					<FiAlertTriangle className="text-3xl text-danger" />
					<h5>Are you sure you want to remove the customer?</h5>
				</div>
			</ModalComponent>
			<div>
				<h5 className="text-primary font-semibold">{`${customer?.firstName} ${customer?.lastName}`}</h5>
				<small>{customer?.email}</small>
			</div>
			<div className="flex gap-2">
				{" "}
				<Button
					isIconOnly
					color="primary"
					variant="ghost"
					className="border w-6 h-6 min-w-6 min-h-6 rounded-full"
					size="sm"
					onPress={() => customer && openEditModal(customer)}
					radius="full"
					startContent={<BiPencil />}
				/>
				<Button
					isIconOnly
					color="primary"
					variant="ghost"
					className="border w-6 h-6 min-w-6 min-h-6 rounded-full"
					size="sm"
					radius="full"
					onPress={onOpen}
					startContent={<BiTrash />}
				/>
			</div>
			<CustomerFormModal
				isOpen={customerFormModal.isOpen}
				onOpenChange={customerFormModal.onOpenChange}
				initialData={customer}
				isEditMode={true}
				isLoading={false}
			/>
		</div>
	);
};

export default SelectedCustomer;

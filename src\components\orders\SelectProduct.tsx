import React, { useEffect, useRef } from "react";
import { useQuery } from "@apollo/client";
import { GET_PRODUCTS } from "../../graphql/products";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";
import { GetProductsType } from "../../types/productType";
import { addToast, Image, Input, Spinner } from "@heroui/react";
import { RadioGroup, useRadio, VisuallyHidden, cn } from "@heroui/react";
import { CiImageOn } from "react-icons/ci";
import { CustomRadio } from "../forms/CustomRadio";
import { BiSearch } from "react-icons/bi";

type SelectProductTypes = {
  productId: string;
  setProductId: (value: string) => void;
};
const SelectProduct = ({ productId, setProductId }: SelectProductTypes) => {
  const [filterValue, setFilterValue] = React.useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { data, loading, error, fetchMore, refetch } = useQuery(GET_PRODUCTS, {
    variables: {
      offset: 0,
      limit: 10,
      filters: {},
    },
    notifyOnNetworkStatusChange: true,
  });

  const { observerRef } = useInfiniteQueryScroll({
    items: data?.getProducts?.products || [],
    totalCount: data?.getProducts?.totalCount || 0,
    loading,
    fetchMore,
    scrollContainerRef,
  });

  // Refetch products whenever the filterValue changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      refetch({
        offset: 0,
        limit: 10,
        filters: {
          search: filterValue,
        },
      });
    }, 300); // Debounce the search to avoid excessive queries
    return () => clearTimeout(timeoutId);
  }, [filterValue, refetch]);

  // Restore focus to the input after refetch
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [data]);

  //   if (loading && !data) return <Spinner className="mt-2" />;
  if (error) {
    addToast({
      title: "Error in fetching products",
      shouldShowTimeoutProgress: true,
      timeout: 2000,
      color: "danger",
    });
    return;
  }

  const onClear = () => {
    setFilterValue("");
  };
  console.log(productId, " proudcgh id");

  return (
    <>
      <Input
        ref={inputRef}
        isClearable
        size="sm"
        classNames={{
          base: "w-full rounded-full",
        }}
        radius="sm"
        placeholder="Search..."
        startContent={<BiSearch />}
        value={filterValue}
        onClear={onClear}
        onValueChange={setFilterValue}
      />
      <div
        ref={scrollContainerRef}
        className="max-h-96 h-60 overflow-y-scroll scrollbar w-full"
      >
        {loading && !data ? (
          <Spinner className="mt-10 text-center w-full" />
        ) : (
          <RadioGroup
            className="w-full"
            value={productId}
            onValueChange={(value) => setProductId(value)}
          >
            {data.getProducts.products.map(
              (product: GetProductsType, index: number) => (
                <div
                  key={product._id}
                  ref={
                    index === data.getProducts.products.length - 1
                      ? observerRef
                      : null
                  }
                  className="w-full flex"
                >
                  <CustomRadio
                    value={product._id}
                    endContent={
                      product.price ? `₹${product.price.toFixed(2)}` : ""
                    }
                  >
                    <div className="flex items-center gap-2">
                      {product?.assets[0]?.path ? (
                        <Image
                          src={
                            product.assets[0]?.path || "/images/placeholder.svg"
                          }
                          alt="product"
                          className="w-10 h-10 rounded-md"
                        />
                      ) : (
                        <CiImageOn className="text-[#3b82f6] text-4xl m-auto" />
                      )}
                      <div className="flex flex-col">
                        <span> {product.name}</span>
                        <span>
                          {" "}
                          {`${product.totalProductQuantity} in stock`}
                        </span>
                      </div>
                    </div>
                  </CustomRadio>
                </div>
              )
            )}
          </RadioGroup>
        )}
        {loading && data ? (
          <Spinner className="mt-2 text-center w-full" />
        ) : null}
      </div>
    </>
  );
};

export default SelectProduct;

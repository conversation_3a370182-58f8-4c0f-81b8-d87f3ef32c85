export type DeliveryChargeType = "CUSTOM" | "NO_CHARGE";
export interface OrderSchema {
	productId: string;
	customerId: string;
	deliveryMethod: DeliveryChargeType;
	customeDeliveryOptions?: {
		name: string;
		rate: number;
		schedule: boolean;
		date?: string;
		fromTime?: string;
		totime?: string;
		pickup: boolean;
	};
	feeOptions: {
		name: string;
		rate: number;
	}[];
	discountOptions: {
		name: string;
		rate: number;
	}[];
	location: {
		useLocation: boolean;
		address: string;
	};
}

export enum PaymentStatus {
	PAID = "PAID",
	UNPAID = "UNPAID",
	PENDING = "PENDING",
	REFUNDED = "REFUNDED",
}

export enum FulfillmentStatus {
	FULFILLED = "FULFILLED",
	UNFULFILLED = "UNFULFILLED",
	PARTIALLY_FULFILLED = "PARTIALLY_FULFILLED",
	PENDING = "PENDING",
}

export interface OrderItem {
	_id: string;
	productId: string;
	productName: string;
	quantity: number;
	price: number;
	total: number;
}

export interface OrderCustomer {
	userId: string;
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
}

export interface CartItem {
	asset: {
		path: string;
		_id: string;
		altText: string;
		type: string;
	};
	finalPrice: number;
	name: string;
	price: number;
	productId: string;
	qty: number;
}

export interface OrderType {
	_id: string;
	orderNo: string;
	createdAt: string;
	userData: OrderCustomer;
	paymentStatus: PaymentStatus;
	fulfillmentStatus: FulfillmentStatus;
	totalprice: number;
	orderPrice: number;
	items: OrderItem[];
	itemcount: number;
	cart: CartItem[];
}

export interface GetOrdersResponse {
	orders: OrderType[];
	totalCount: number;
}

export interface OrderFilters {
	orderNo: string | null;
	userId: string | null;
	isManual: boolean | null;
	isArchived: boolean | null;
	maxTotalPrice: number | null;
	minTotalPrice: number | null;
	paymentStatus: PaymentStatus | null;
	fulfillmentStatus: FulfillmentStatus | null;
	searchTerm: string | null;
	startDate: string | null;
	endDate: string | null;
	tagIds: string[] | null;
}

export interface ColumnType {
	name: string;
	uid: string;
	sortable?: boolean;
}

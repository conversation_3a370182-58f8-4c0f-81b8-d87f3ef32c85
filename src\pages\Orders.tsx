import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import TableComponent from "../components/Table";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";
import { useNavigate } from "react-router-dom";
import { useBoundStore } from "../store/store";
import { ColumnType, FulfillmentStatus, OrderType, PaymentStatus } from "../types/orderType";
import { RxDividerVertical } from "react-icons/rx";
import OrdersFilters from "../components/orders/OrdersFilters";
import { useMutation, useQuery } from "@apollo/client";
import { useInfiniteQueryScroll } from "../hooks/useInfiniteQueryScroll";
import { LiaTagSolid } from "react-icons/lia";
import { useOrderModals } from "../hooks/useOrderModals";
import ModalComponent from "../components/ModalComponent";
import EditOrderTagsModal from "../components/orders/EditOrderTagsModal";
import { UPDATE_ORDER_TAG } from "../graphql/orderTags";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";
import { FETCH_ORDERS } from "../graphql/orders";
import { formatDateNative } from "../helpers/formatters";

// Define columns for the orders table
export const columns: ColumnType[] = [
	{ name: "Order", uid: "orderNo", sortable: true },
	{ name: "Customer", uid: "userData.firstName", sortable: false },
	{ name: "Date created", uid: "createdAt", sortable: true },
	{ name: "Payment", uid: "paymentStatus", sortable: false },
	{ name: "Fulfillment", uid: "fulfillmentStatus", sortable: false },
	{ name: "Total", uid: "orderPrice", sortable: true },
	{ name: "Items", uid: "itemcount", sortable: false },
];

const Orders = () => {
	const navigate = useNavigate();
	const { orderTagsModal, mainModal } = useOrderModals();
	// Get state from Zustand store
	const {
		orderVisibleColumns,
		setOrderVisibleColumns,
		selectedOrderKeys,
		setSelectedOrderKeys,
		orderFilterText,
		setOrderFilterText,
		orderFilters,
		setOrderFilters,
		selectedOrderTagId,
		setSelectedOrderTagId,
	} = useBoundStore();
	const { data, loading, fetchMore, refetch } = useQuery(FETCH_ORDERS, {
		// API_CHANGE need to change the query here
		variables: {
			limit: 10,
			offset: 0,
			filter: {
				searchTerm: orderFilterText,
			},
		},
		notifyOnNetworkStatusChange: true,
		fetchPolicy: "cache-and-network", // use 'cache-and-network' or 'network-only'
	});

	// API_CHANGE need to change the query here for the bulk orders
	const [
		updateOrderTag,
		{ data: updateOrderTagData, loading: updateOrderTagLoading, error: updateOrderTagError },
	] = useMutation(UPDATE_ORDER_TAG);

	useMutationHandler({
		data: updateOrderTagData,
		loading: updateOrderTagLoading,
		error: updateOrderTagError,
		successMessage: "Order tag assigned successfully",
		onClose: orderTagsModal.onClose,
	});

	const handleAction = () => {
		//API_CHANGE need to add the refetch logic here for the filters change
		refetch({
			limit: 10,
			offset: 0,
			filter: {
				paymentStatus: orderFilters.paymentStatus,
				fulfillmentStatus: orderFilters.fulfillmentStatus,
				searchTerm: orderFilters.searchTerm,
				startDate: orderFilters.startDate,
				endDate: orderFilters.endDate,
				tagIds: orderFilters.tagIds,
				isManual: orderFilters.isManual,
				isArchived: orderFilters.isArchived,
				orderNo: orderFilters.orderNo,
				userId: orderFilters.userId,
				maxTotalPrice: orderFilters.maxTotalPrice,
				minTotalPrice: orderFilters.minTotalPrice,
			},
		}).then(() => mainModal.onClose());
	};

	//Render cell content based on column key
	const renderCell = useCallback((order: OrderType, columnKey: keyof OrderType | string) => {
		const cellValue = order[columnKey as keyof OrderType];

		switch (columnKey) {
			case "orderNo":
				return order.orderNo || "-";
			case "createdAt":
				return order.createdAt ? formatDateNative(order.createdAt) : "-";
			case "userData.firstName":
				return `${order.userData.firstName} ${order.userData.lastName}`;
			case "paymentStatus":
				return (
					<Chip
						className="uppercase"
						color={order.paymentStatus === PaymentStatus.PAID ? "success" : "danger"}
						size="sm"
						variant="flat"
					>
						{order.paymentStatus === PaymentStatus.PAID ? "Paid" : "Unpaid"}
					</Chip>
				);
			case "fulfillmentStatus":
				return (
					<Chip
						className="capitalize"
						color={order.fulfillmentStatus === FulfillmentStatus.FULFILLED ? "success" : "danger"}
						size="sm"
						variant="flat"
					>
						{order.fulfillmentStatus.toLowerCase()}
					</Chip>
				);
			case "orderPrice":
				return order.orderPrice ? `₹${order.orderPrice.toFixed(2)}` : "-";
			case "itemcount":
				return (
					<div className="flex gap-x-2 items-center  group">
						<div>{order.itemcount ? order.itemcount : "-"}</div>
						<BiChevronRight
							className="text-2xl opacity-0 group-hover:opacity-100 text-primary !font-bold  group-hover:cursor-pointer"
							onClick={() => navigate(`/admin/sales/orders/${order._id}`)}
						/>
					</div>
				);

			default:
				return typeof cellValue === "object" ? JSON.stringify(cellValue) : cellValue;
		}
	}, []);

	const finalSelectedKeys = useMemo(() => {
		if (selectedOrderKeys === "all") {
			return data?.getOrdersByFilters.data.map((item: OrderType) => item._id);
		}
		return Array.from(selectedOrderKeys.values());
	}, [selectedOrderKeys]);

	// Top content for the table (search, filters, etc.)
	const topContent = useMemo(() => {
		return (
			<TopHeader
				columns={columns}
				filterValue={orderFilterText}
				onOpen={mainModal.onOpen}
				onSearchChange={setOrderFilterText}
				setVisibleColumns={(cols) => setOrderVisibleColumns(cols)}
				visibleColumns={orderVisibleColumns}
				showItems={{
					columnsToggle: true,
					exportImportButton: false,
					filters: true,
					searchBar: true,
				}}
			/>
		);
	}, [orderFilterText, orderVisibleColumns]);

	// Top content when rows are selected
	const topSelectedContent = useMemo(() => {
		return (
			<div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
				<p>
					{selectedOrderKeys === "all"
						? data?.getOrdersByFilters?.data.length
						: selectedOrderKeys.size}{" "}
					of {data?.getOrdersByFilters?.data.length} Selected
				</p>{" "}
				<RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
				<div className="flex gap-x-3">
					<Button
						color="primary"
						radius="full"
						size="sm"
						variant="ghost"
						className="border"
						onPress={() => {
							setSelectedOrderTagId(null);
							orderTagsModal.onOpen();
						}}
						startContent={<LiaTagSolid className="text-xl" />}
					>
						Assign Tags
					</Button>
					<Button
						color="primary"
						radius="full"
						size="sm"
						variant="flat"
						onPress={() => {
							console.log("Bulk action on selected orders");
							// Implement bulk actions here
						}}
					>
						Bulk Actions
					</Button>
				</div>
			</div>
		);
	}, [selectedOrderKeys]); //API_CHANGE will need to add the change here

	const scrollContainerRef = useRef<HTMLDivElement | null>(null);
	const { hasMore, loading: isLoading } = useInfiniteQueryScroll({
		items: data?.getOrdersByFilters?.data || [],
		totalCount: data?.getOrdersByFilters?.totalCount || 0,
		loading,
		fetchMore,
		scrollContainerRef, // pass in the ref
	});

	return (
		<>
			<ModalComponent
				isOpen={orderTagsModal.isOpen}
				onOpenChange={orderTagsModal.onOpenChange}
				id="order-tags-modal"
				size="lg"
				modalHeader="Assign Tags to Orders"
				isLoading={updateOrderTagLoading}
				disabled={selectedOrderTagId === null || selectedOrderTagId === ""}
				onPress={() => {
					// Update orders with the selected tag
					const orderIds = finalSelectedKeys;
					if (orderIds.length > 0 && selectedOrderTagId) {
						// API_CHANGE: Add bulk update orders API call here
						updateOrderTag({
							variables: {
								updateOrderTagId: selectedOrderTagId,
								input: {
									orderIds: orderIds,
								},
							},
						}).then(() => {
							orderTagsModal.onClose();
							setSelectedOrderTagId(null);
						});
					}
				}}
			>
				<EditOrderTagsModal />
			</ModalComponent>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<Breadcrumbs>
					<BreadcrumbItem>Dashboard</BreadcrumbItem>
					<BreadcrumbItem>Orders</BreadcrumbItem>
				</Breadcrumbs>
			</div>

			<TopDrawer
				isOpen={mainModal.isOpen}
				onOpenChange={mainModal.onOpenChange}
				drawerHeader="Filters"
				isLoading={false}
				handleAction={handleAction}
			>
				<OrdersFilters filters={orderFilters} setFilters={setOrderFilters} />
			</TopDrawer>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<h1>
					Orders{" "}
					{data?.getOrdersByFilters?.totalCount
						? `(${data?.getOrdersByFilters?.totalCount})`
						: null}
				</h1>
				<Button
					size="sm"
					radius="full"
					color="primary"
					startContent={<BiPlus />}
					onPress={() => navigate("/admin/sales/add-order")}
				>
					Add New Order
				</Button>
			</div>

			<TableComponent<OrderType>
				columns={columns}
				renderCell={renderCell}
				topContent={topContent}
				topSelectedContent={topSelectedContent}
				list={{ items: data?.getOrdersByFilters?.data || [] }}
				visibleColumns={orderVisibleColumns}
				isLoading={loading || isLoading}
				hasMore={hasMore}
				selectedKeys={selectedOrderKeys}
				setSelectedKeys={setSelectedOrderKeys}
				handleLoadMore={() =>
					fetchMore({
						variables: {
							offset: data?.getOrdersByFilters?.data.length || 0,
						},
					})
				}
				tableClassName="ordersTable"
			/>
		</>
	);
};

export default Orders;

import { useRef } from "react";
import ModalComponent from "../ModalComponent";
import CustomerForm from "./CustomerForm";
import { CustomerFormData } from "../../validation/customerValidationSchema";
import { CustomerMemberStatus, CustomerType } from "../../types/customerType";
import { useMutationHandler } from "../../hooks/useMutationStatusHandler";
import { useMutation } from "@apollo/client";
import { GET_CUSTOMERS, SAVE_CUSTOMERS } from "../../graphql/customers";

interface CustomerFormModalProps {
	isOpen: boolean;
	onOpenChange: () => void;
	initialData?: CustomerType | null;
	isEditMode?: boolean;
	isLoading?: boolean;
}

const CustomerFormModal = ({
	isOpen,
	onOpenChange,
	initialData = null,
	isEditMode = false,
	isLoading = false,
}: CustomerFormModalProps) => {
	const formRef = useRef<HTMLFormElement>(null);
	console.log(isEditMode, " is edit mode");

	// Save customer mutation
	const [
		saveAdminSideUser,
		{ data: saveCustomerData, loading: saveCustomerLoading, error: saveCustomerError },
	] = useMutation(SAVE_CUSTOMERS, {
		refetchQueries: [GET_CUSTOMERS, "GetUsersByFilters"],
	});

	// Handle save customer mutation response
	useMutationHandler({
		data: saveCustomerData,
		loading: saveCustomerLoading,
		error: saveCustomerError,
		successMessage: "Customer saved successfully",
	});
	const handleSubmit = (data: CustomerFormData) => {
		console.log(" i am called in the mutation");
		const input = {
			firstName: data.firstName,
			lastName: data.lastName,
			email: data.email || "",
			phoneNumber: data.phoneNumber || "",
			gender: data.gender,
			dob: data.dob || "",
			emailSubscribedStatus: data.emailSubscribedStatus || CustomerMemberStatus.NEVER_SUBSCRIBED,
			labelIds: data.labelIds || [],
			// Add other fields as needed
		};

		if (!isEditMode) {
			saveAdminSideUser({
				variables: {
					input,
				},
			}).then(() => {
				onOpenChange();
			});
		} else {
			console.log("updating the customer");
			saveAdminSideUser({
				variables: {
					input,
					saveAdminSideUserId: initialData?._id,
				},
			}).then(() => {
				onOpenChange();
			});
		}
	};

	return (
		<ModalComponent
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			modalHeader={isEditMode ? "Edit customer" : "Create a new contact"}
			saveButtonText="Save"
			size="2xl"
			isLoading={isLoading}
			onPress={() => {
				// Properly submit the form to trigger validation
				if (formRef.current) {
					formRef.current.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }));
				}
			}}
			className={{
				saveColor: "primary",
			}}
		>
			<div className="max-h-[70vh] scrollbar overflow-y-auto px-1">
				<CustomerForm
					initialData={initialData}
					onSubmit={handleSubmit}
					isLoading={isLoading}
					formRef={formRef as React.RefObject<HTMLFormElement>}
				/>
			</div>
		</ModalComponent>
	);
};

export default CustomerFormModal;
